extends Node

# Firebase configuration
const DEFAULT_DATABASE_URL = "https://cjsc-a3e91-default-rtdb.firebaseio.com"
var current_database_url = DEFAULT_DATABASE_URL

# Game state
var current_game_id = ""
var current_room_code = ""
var last_firebase_status = ""

# HTTP request nodes
var database_request
var game_listener
var players_listener
var answers_listener

# Signals
signal game_created(game_id, room_code)
signal player_joined(player_name)
signal player_answer_selected(player_name, answer_index)
signal player_response_used(player_name, response_index)
signal database_error(error_code, error_message)
signal game_status_changed(new_status)
signal submitted_responses_updated(submitted_responses_data)
signal judge_index_updated(new_judge_index)

func _ready():
	# Initialize HTTP request nodes
	database_request = HTTPRequest.new()
	game_listener = HTTPRequest.new()
	players_listener = HTTPRequest.new()
	answers_listener = HTTPRequest.new()

	add_child(database_request)
	add_child(game_listener)
	add_child(players_listener)
	add_child(answers_listener)

	# Connect signals
	database_request.request_completed.connect(_on_database_request_completed)
	game_listener.request_completed.connect(_on_game_listener_completed)
	players_listener.request_completed.connect(_on_players_listener_completed)
	answers_listener.request_completed.connect(_on_answers_listener_completed)

	print("Firebase manager initialized")

# Generate a random 5-letter room code
func generate_room_code():
	print("\n\n[ROOM CODE DEBUG] Firebase manager: generate_room_code called\n\n")

	# Check if we already have a room code
	if current_room_code != "" and current_room_code.length() == 5:
		print("[ROOM CODE DEBUG] Firebase manager: Already have a room code: ", current_room_code)
		print("[ROOM CODE DEBUG] Firebase manager: Returning existing room code instead of generating a new one")
		return current_room_code

	var first_letter_chars = "AM" # First letter must be A or M
	var chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789" # Removed similar looking characters
	var code = ""
	var rng = RandomNumberGenerator.new()
	rng.randomize()

	# Set first letter to A or M
	var first_idx = rng.randi_range(0, first_letter_chars.length() - 1)
	code += first_letter_chars[first_idx]

	# Generate the remaining 4 random characters
	for i in range(4):
		var idx = rng.randi_range(0, chars.length() - 1)
		code += chars[idx]

	print("\n\n[ROOM CODE DEBUG] Firebase manager: Generated NEW room code: ", code, "\n\n")
	return code

# Create a new game in Firebase
func create_game(response_indices_by_player = {}):
	# Generate a random 5-letter room code
	var room_code = generate_room_code()
	current_room_code = room_code
	print("[ROOM CODE DEBUG] Firebase generated room code: ", room_code)

	# Create game object
	var game_data = {
		"timestamp": Time.get_unix_time_from_system(),
		"room_code": room_code,
		"status": "Waiting for Players",
		"players": [],
		"player_scores": {},
		"player_answers": {},
		"response_indices": response_indices_by_player,
		"submitted_responses": []
	}

	# Save to Firebase
	var url = current_database_url + "/.json"
	print("Database URL: ", url)

	var body = JSON.stringify(game_data)
	print("Game data: ", body)

	var headers = ["Content-Type: application/json"]

	print("Sending game data to Firebase with room code: ", room_code)
	var error = database_request.request(url, headers, HTTPClient.METHOD_POST, body)
	if error != OK:
		print("HTTP Request Error: ", error)

		# Error code 44 is a common network-related error that might be a false positive
		# We'll log it but continue with the game creation process
		if error == 44:
			print("Warning: Got error code 44, but this might be a false positive. Continuing...")
		else:
			emit_signal("database_error", error, "Failed to create game")
			return

	print("Game creation request sent successfully")

# Handle database request completion
func _on_database_request_completed(result, response_code, headers, body):
	print("Database request completed with result: ", result, ", response code: ", response_code)

	# Check for successful result
	if result != HTTPRequest.RESULT_SUCCESS:
		print("Database request failed with result: ", result)

		# Don't treat this as a fatal error, just log it and continue
		print("Warning: Database request failed, but we'll try to continue anyway")

	# Check for valid response code
	if response_code != 200:
		print("Database request failed with response code: ", response_code)

		# Don't treat this as a fatal error, just log it and continue
		print("Warning: Got non-200 response code, but we'll try to continue anyway")

	var response_text = body.get_string_from_utf8()
	print("Response: ", response_text)

	var response = JSON.parse_string(response_text)
	if response == null:
		print("Failed to parse JSON response")
		emit_signal("database_error", -1, "Failed to parse JSON response")
		return

	if response.has("name"):
		current_game_id = response.name
		print("Game created with ID: ", current_game_id)

		# Start listening for changes
		start_game_listeners()

		# Emit signal with game ID and room code
		emit_signal("game_created", current_game_id, current_room_code)
	else:
		print("Response missing game ID")
		emit_signal("database_error", -1, "Response missing game ID")

# Get the current room code
func get_room_code():
	print("[ROOM CODE DEBUG] Firebase manager: get_room_code returning: ", current_room_code)
	return current_room_code

# Start listening for changes to the game
func start_game_listeners():
	print("Starting game listeners for game ID: ", current_game_id)

	# Check if we have a valid game ID
	if current_game_id == "":
		print("Cannot start listeners: No game ID")
		return

	# Listen for game changes
	var game_url = current_database_url + "/" + current_game_id + ".json"
	print("Game listener URL: ", game_url)

	var error = game_listener.request(game_url)
	if error != OK:
		print("Game listener request error: ", error)
		return

# Handle game listener completion
func _on_game_listener_completed(result, response_code, headers, body):
	if result != HTTPRequest.RESULT_SUCCESS:
		print("Game listener failed with result: ", result)
		print("Warning: Game listener failed, but we'll try to continue anyway")

	if response_code != 200:
		print("Game listener failed with response code: ", response_code)
		print("Warning: Game listener got non-200 response code, but we'll try to continue anyway")

	# Process the response
	var response_text = body.get_string_from_utf8()

	# Parse the response
	var response = JSON.parse_string(response_text)
	if response != null and typeof(response) == TYPE_DICTIONARY:
		# Check for game status changes
		if response.has("status"):
			var new_status = response.status

			# Only emit the signal if the status has changed
			if new_status != last_firebase_status:
				print("Status changed from ", last_firebase_status, " to ", new_status)
				last_firebase_status = new_status
				emit_signal("game_status_changed", new_status)

		# Check for used responses
		if response.has("used_responses") and typeof(response.used_responses) == TYPE_DICTIONARY:
			var used_responses = response.used_responses
			print("Found used responses: ", used_responses)

			# Store the last processed used responses to avoid duplicate processing
			var last_processed_used_responses = {}

			# Process each player's used responses
			for player_name in used_responses.keys():
				var player_used_indices = used_responses[player_name]
				if typeof(player_used_indices) == TYPE_ARRAY and player_used_indices.size() > 0:
					print("Player ", player_name, " has used responses: ", player_used_indices)

					# Get the last processed indices for this player
					var last_processed_indices = []
					if last_processed_used_responses.has(player_name):
						last_processed_indices = last_processed_used_responses[player_name]

					# Process only new indices
					for index in player_used_indices:
						if not index in last_processed_indices:
							print("Processing NEW used response index ", index, " for player ", player_name)
							emit_signal("player_response_used", player_name, index)

							# Add to last processed indices
							last_processed_indices.append(index)

					# Update last processed indices for this player
					last_processed_used_responses[player_name] = last_processed_indices

			# Clear used responses after processing to ensure they're only processed once
			if used_responses.size() > 0:
				print("Clearing used responses after processing")
				update_game_data("used_responses", {})

		# Check for submitted responses changes
		if response.has("submitted_responses") and typeof(response.submitted_responses) == TYPE_ARRAY:
			var submitted_responses = response.submitted_responses
			print("Firebase submitted responses array: ", submitted_responses)

			# Emit signal with submitted responses data
			if submitted_responses.size() > 0:
				print("Emitting submitted_responses_updated signal with ", submitted_responses.size(), " responses")
				emit_signal("submitted_responses_updated", submitted_responses)

		# Check for judge index changes
		if response.has("current_judge_index"):
			var new_judge_index = response.current_judge_index
			print("Firebase judge index: ", new_judge_index)

			# Emit the dedicated judge index updated signal
			emit_signal("judge_index_updated", new_judge_index)

		# Check for player changes
		if response.has("players") and typeof(response.players) == TYPE_ARRAY:
			var players = response.players
			print("Firebase players array: ", players)

			# Notify about each player
			for player_name in players:
				if typeof(player_name) == TYPE_STRING:
					print("Emitting player_joined signal for: ", player_name)
					emit_signal("player_joined", player_name)

	# Restart the listener after a short delay
	await get_tree().create_timer(1.0).timeout
	if current_game_id != "":
		var game_url = current_database_url + "/" + current_game_id + ".json"
		game_listener.request(game_url)

# Handle players listener completion
func _on_players_listener_completed(result, response_code, headers, body):
	# Placeholder for player updates
	pass

# Handle answers listener completion
func _on_answers_listener_completed(result, response_code, headers, body):
	# Placeholder for answer updates
	pass

# Update the game data in Firebase
func update_game_data(path, data):
	print("Updating game data")
	print("Path: ", path)

	# Check if we have a valid game ID
	if current_game_id == "":
		print("ERROR: Cannot update game data: No game ID")
		return

	var url = current_database_url + "/" + current_game_id + "/" + path + ".json"
	print("Update URL: ", url)

	var body = JSON.stringify(data)
	print("Update data: ", body)

	var update_request = HTTPRequest.new()
	add_child(update_request)
	update_request.request_completed.connect(func(result, response_code, headers, body):
		print("Update request completed with result: ", result, ", response code: ", response_code)

		if result != HTTPRequest.RESULT_SUCCESS:
			print("Update request failed with result: ", result)
			print("Warning: Update request failed, but we'll try to continue anyway")

		if response_code != 200:
			print("Update request failed with response code: ", response_code)
			print("Warning: Update request got non-200 response code, but we'll try to continue anyway")

		# Always consider the update successful for our purposes
		print("Treating update as successful regardless of response")

		# Special handling for status updates
		if path == "status":
			last_firebase_status = data
			emit_signal("game_status_changed", data)

		update_request.queue_free()
	)

	var headers = ["Content-Type: application/json"]
	var error = update_request.request(url, headers, HTTPClient.METHOD_PATCH, body)
	if error != OK:
		print("Update request error: ", error)
		update_request.queue_free()
		return

# Game state update functions
func update_game_status(status):
	print("Updating game status to: ", status)
	update_game_data("status", status)

func update_current_round(round_number):
	print("Updating current round to: ", round_number)
	update_game_data("current_round", round_number)

func update_current_judge(judge_name):
	print("Updating current judge to: ", judge_name)
	update_game_data("current_judge", judge_name)

func update_current_judge_index(judge_index):
	print("Updating current judge index to: ", judge_index)
	update_game_data("current_judge_index", judge_index)

func update_current_prompt(prompt):
	print("Updating current prompt: ", prompt)

	# Make sure the prompt is in the correct format for Firebase
	var formatted_prompt

	if typeof(prompt) == TYPE_DICTIONARY and prompt.has("prompt"):
		# Already in the correct format
		formatted_prompt = prompt
		print("Prompt already in correct format: ", formatted_prompt)
	else:
		# Convert to the correct format
		formatted_prompt = {"prompt": str(prompt)}
		print("Converted prompt to correct format: ", formatted_prompt)

	# Update the prompt in Firebase
	update_game_data("current_prompt", formatted_prompt)

func update_timer_end(end_time):
	print("Updating timer end to: ", end_time)
	update_game_data("timer_end", end_time)

func update_player_response_indices(player_name, indices):
	print("Updating response indices for player: ", player_name)
	var path = "response_indices/" + player_name
	update_game_data(path, indices)

func update_player_answer(player_name, answer):
	print("Updating player answer for: ", player_name)
	var path = "player_answers/" + player_name
	update_game_data(path, answer)

func update_game_winner(winner_name):
	print("Updating game winner to: ", winner_name)
	update_game_data("winner", winner_name)

func update_winning_response(response):
	print("Updating winning response")
	update_game_data("winning_response", response)

# Add a player to the game
func add_player(player_name):
	print("Adding player: ", player_name)

	# Get current players array
	var get_players_request = HTTPRequest.new()
	add_child(get_players_request)

	var url = current_database_url + "/" + current_game_id + "/players.json"

	get_players_request.request_completed.connect(func(result, response_code, headers, body):
		# Handle errors gracefully
		if result != HTTPRequest.RESULT_SUCCESS:
			print("Get players request failed with result: ", result)
			print("Warning: Get players request failed, but we'll try to continue anyway")

			# Create an empty players array and proceed
			var players_array = []
			players_array.append(player_name)

			# Update the players array
			update_game_data("players", players_array)

			# Initialize player score
			update_game_data("player_scores/" + player_name, 0)

			# Emit signal
			emit_signal("player_joined", player_name)
			get_players_request.queue_free()
			return

		if response_code != 200:
			print("Get players request failed with response code: ", response_code)
			print("Warning: Get players request got non-200 response code, but we'll try to continue anyway")

			# Create an empty players array and proceed
			var players_array = []
			players_array.append(player_name)

			# Update the players array
			update_game_data("players", players_array)

			# Initialize player score
			update_game_data("player_scores/" + player_name, 0)

			# Emit signal
			emit_signal("player_joined", player_name)
			get_players_request.queue_free()
			return

		# If we got here, the request was successful
		var response_text = body.get_string_from_utf8()
		var players_array = JSON.parse_string(response_text)

		if players_array == null:
			players_array = []

		# Add the new player if not already in the array
		if not player_name in players_array:
			players_array.append(player_name)

			# Update the players array
			update_game_data("players", players_array)

			# Initialize player score
			update_game_data("player_scores/" + player_name, 0)

			# Emit signal
			emit_signal("player_joined", player_name)

		get_players_request.queue_free()
	)

	get_players_request.request(url)

# Get game data
func get_game_winner():
	# Check if we have a valid game ID
	if current_game_id == "":
		print("ERROR: Cannot get game winner: No game ID")
		return "No game available"

	# Create a synchronous HTTP request
	var http_request = HTTPRequest.new()
	add_child(http_request)

	# Build the URL to fetch the winner field
	var url = current_database_url + "/" + current_game_id + "/winner.json"
	print("Getting game winner from URL: ", url)

	# Make the request
	var error = http_request.request(url)
	if error != OK:
		print("Error requesting game winner: ", error)
		http_request.queue_free()
		return "Error fetching winner"

	# Wait for the request to complete
	var result = await http_request.request_completed

	var response_code = result[1]
	var body = result[3]

	if response_code != 200:
		print("Error getting game winner, response code: ", response_code)
		http_request.queue_free()
		return "Error: " + str(response_code)

	# Parse the response
	var response_text = body.get_string_from_utf8()
	var winner = JSON.parse_string(response_text)

	print("Got game winner from Firebase: ", winner)

	# Clean up
	http_request.queue_free()

	# Handle null or invalid response
	if winner == null:
		return "No winner available"

	return winner

func get_current_judge_index():
	# Check if we have a valid game ID
	if current_game_id == "":
		print("ERROR: Cannot get current judge index: No game ID")
		return -1

	# Create a synchronous HTTP request
	var http_request = HTTPRequest.new()
	add_child(http_request)

	# Build the URL to fetch the current_judge_index field
	var url = current_database_url + "/" + current_game_id + "/current_judge_index.json"
	print("Fetching current judge index from Firebase: " + url)

	# Make the request
	var error = http_request.request(url)
	if error != OK:
		print("Error requesting current judge index: " + str(error))
		http_request.queue_free()
		return -1

	# Wait for the request to complete
	var result = await http_request.request_completed

	var response_code = result[1]
	var body = result[3]

	if response_code != 200:
		print("Error fetching judge index, response code: " + str(response_code))
		http_request.queue_free()
		return -1

	var judge_index = JSON.parse_string(body.get_string_from_utf8())

	if judge_index == null or typeof(judge_index) != TYPE_INT:
		print("Invalid judge index received, defaulting to 0")
		judge_index = 0

	print("Fetched judge index: " + str(judge_index))

	http_request.queue_free()

	return judge_index

func get_winning_response():
	# Check if we have a valid game ID
	if current_game_id == "":
		print("ERROR: Cannot get winning response: No game ID")
		return "No game available"

	# Create a synchronous HTTP request
	var http_request = HTTPRequest.new()
	add_child(http_request)

	# Build the URL to fetch the winning_response field
	var url = current_database_url + "/" + current_game_id + "/winning_response.json"
	print("Getting winning response from URL: ", url)

	# Make the request
	var error = http_request.request(url)
	if error != OK:
		print("Error requesting winning response: ", error)
		http_request.queue_free()
		return "Error fetching response"

	# Wait for the request to complete
	var result = await http_request.request_completed

	var response_code = result[1]
	var body = result[3]

	if response_code != 200:
		print("Error getting winning response, response code: ", response_code)
		http_request.queue_free()
		return "Error: " + str(response_code)

	# Parse the response
	var response_text = body.get_string_from_utf8()
	var winning_response = JSON.parse_string(response_text)

	print("Got winning response from Firebase: ", winning_response)

	# Clean up
	http_request.queue_free()

	# Handle null or invalid response
	if winning_response == null:
		return "No winning response available"

	return winning_response

# Get players from Firebase
func get_players():
	# Make a synchronous request to get the players array
	var http_request = HTTPRequest.new()
	add_child(http_request)

	var url = current_database_url + "/" + current_game_id + "/players.json"
	print("Getting players from URL: ", url)

	var error = http_request.request(url)
	if error != OK:
		print("Error requesting players: ", error)
		http_request.queue_free()
		return []

	# Wait for the request to complete
	var result = await http_request.request_completed

	var response_code = result[1]
	var body = result[3]

	if response_code != 200:
		print("Error getting players, response code: ", response_code)
		http_request.queue_free()
		return []

	var response_text = body.get_string_from_utf8()
	var players = JSON.parse_string(response_text)

	print("Got players from Firebase: ", players)

	http_request.queue_free()

	if players == null:
		return []

	return players

# Ensure judge index is updated only at the start of a new round
func update_judge_index_at_round_start():
	var judge_index = await get_current_judge_index()
	if judge_index >= 0:
		emit_signal("game_status_changed", "Judge index updated to: " + str(judge_index))
		print("Judge index updated at round start: " + str(judge_index))
