extends Node

const Player = preload("res://cardGame/player.gd")
const CardScene = preload("res://cardGame/Card.tscn")
const PlayerUIScene = preload("res://cardGame/PlayerUI.tscn")

var players: Array = []
var prompts: Array = []
var responses: Array = []
var current_judge_index: int = 0  # Kept for compatibility, but no longer used for judging
var current_prompt: Dictionary = {}
var judge_timer: Timer
var played_cards = {}  # Made this a class variable so it can be accessed in the timeout function

# Multiplayer variables
var multiplayer_manager
var is_multiplayer_game = false
var room_code = ""
var waiting_for_players = false
var current_round = 0
var game_status = "Waiting for Players"
var max_score = 5  # Number of rounds to win the game
var timer_end = 0   # When the current timer will end
var last_processed_status = ""  # Track the last processed game status to prevent duplicate processing
var is_processing_state = false  # Flag to prevent concurrent state processing
var last_timer_update = 0  # Track the last timer update to prevent UI flashing
var prompt_sent_for_round = 0  # Track which round we've already sent a prompt for
var should_display_responses = false  # Flag to control when responses should be displayed
var last_displayed_responses_count = 0  # Track how many responses we've displayed to avoid duplicates

# Add a new variable to track used prompts
var used_prompt_indices = []  # Tracks which indices have been used
var prompt_selection_index = 0  # Current index for sequential selection

# UI references
@onready var prompt_label = $CurrentPromptDisplay/Prompt
@onready var judge_label = $Judge
@onready var timer_label = $Timer/Label
@onready var player_container = $HBoxContainer
@onready var cards_container = $HBoxContainer2
@onready var played_cards_container = $TextureRect
@onready var room_code_display = $RoomCodeDisplay
@onready var current_prompt_display = $CurrentPromptDisplay
@onready var winning_response_display = $WinningResponseDisplay
@onready var judge_asks_label = $JudgeAsksLabel

func _ready():
	# Add a key press handler for testing
	set_process_input(true)
	# Create judge timer if it doesn't exist
	if not judge_timer:
		judge_timer = Timer.new()
		judge_timer.name = "JudgeTimer"
		judge_timer.one_shot = true
		judge_timer.wait_time = 30.0
		judge_timer.timeout.connect(_on_judge_timer_timeout)
		add_child(judge_timer)
		print("Judge timer created")

	# Make sure the current_prompt_display is properly initialized
	current_prompt_display = get_node_or_null("CurrentPromptDisplay")
	if current_prompt_display:
		print("CurrentPromptDisplay found in scene")
		# Initially hide it until we have a prompt
		current_prompt_display.visible = false
		print("Set CurrentPromptDisplay.visible = false initially")
	else:
		print("ERROR: CurrentPromptDisplay node not found in scene!")
		# Try to find it by searching all children
		for child in get_children():
			print("Child node: ", child.name)
			if child.name == "CurrentPromptDisplay":
				print("Found CurrentPromptDisplay in children")
				current_prompt_display = child
				current_prompt_display.visible = false
				print("Set CurrentPromptDisplay.visible = false initially")
				break

	# Make sure the judge_asks_label is properly initialized
	judge_asks_label = get_node_or_null("JudgeAsksLabel")
	if judge_asks_label:
		print("JudgeAsksLabel found in scene")
		# Initially hide it until we have a prompt
		judge_asks_label.visible = false
		print("Set JudgeAsksLabel.visible = false initially")
	else:
		print("ERROR: JudgeAsksLabel node not found in scene!")
		# Try to find it by searching all children
		for child in get_children():
			if child.name == "JudgeAsksLabel":
				print("Found JudgeAsksLabel in children")
				judge_asks_label = child
				judge_asks_label.visible = false
				print("Set JudgeAsksLabel.visible = false initially")
				break

	# Create a timer for updating the UI if it doesn't exist
	var existing_ui_timer = get_node_or_null("UITimer")
	if not existing_ui_timer:
		var ui_timer = Timer.new()
		ui_timer.name = "UITimer"
		ui_timer.one_shot = false
		ui_timer.wait_time = 0.5  # Update twice per second
		ui_timer.timeout.connect(_update_timer_display)
		ui_timer.autostart = true
		add_child(ui_timer)
		print("UI timer created with wait time: ", ui_timer.wait_time)

	# Create a timer for checking for new responses
	var existing_response_timer = get_node_or_null("ResponseCheckTimer")
	if not existing_response_timer:
		var response_timer = Timer.new()
		response_timer.name = "ResponseCheckTimer"
		response_timer.one_shot = false
		response_timer.wait_time = 0.5  # Check every half second for more responsive updates
		response_timer.timeout.connect(_check_for_new_responses)
		response_timer.autostart = true
		add_child(response_timer)
		print("Response check timer created with 0.5 second interval")

	# Check if UI elements exist
	if not prompt_label or not judge_label or not timer_label or not played_cards_container:
		push_error("UI elements not found. Check scene structure.")
		return

	# Hide player container - we don't want to show player columns
	if player_container:
		player_container.visible = false

	# Hide cards container - we don't need this either
	if cards_container:
		cards_container.visible = false

	# Hide room code display by default
	if room_code_display:
		room_code_display.visible = false

	# Hide timer label by default
	if timer_label:
		timer_label.visible = false

	# Load prompts and responses
	prompts = load_prompts()
	responses = load_responses()

	# Debug information
	print("Loaded prompts: ", prompts.size())
	print("Loaded responses: ", responses.size())

	# Shuffle the cards
	randomize()
	prompts.shuffle()
	responses.shuffle()

	# Check if multiplayer manager exists
	multiplayer_manager = get_node_or_null("/root/MultiplayerManager")
	if multiplayer_manager:
		print("Multiplayer manager found, initializing multiplayer game")
		is_multiplayer_game = true
		multiplayer_manager.initialize_game(self)

		# Connect to game state changes
		if multiplayer_manager.firebase:
			print("Attempting to connect to game_status_changed signal")
			var signal_list = multiplayer_manager.firebase.get_signal_list()
			print("Available signals: ", signal_list)

			# Check if the signal exists
			var signal_found = false  # Renamed to avoid shadowing
			for sig in signal_list:
				if sig["name"] == "game_status_changed":
					signal_found = true
					break

			if signal_found:  # Use the renamed variable
				print("Signal game_status_changed found, connecting...")
				multiplayer_manager.firebase.game_status_changed.connect(handle_game_state_change)
				print("Signal connected successfully")
			else:
				push_error("Signal game_status_changed not found in firebase_manager")

		# Connect directly to the firebase manager for status changes
		if multiplayer_manager.firebase:
			print("Connecting directly to firebase game_status_changed signal")
			multiplayer_manager.firebase.game_status_changed.connect(self._on_direct_game_status_changed)

			# Connect to the player_response_used signal
			print("Connecting to player_response_used signal")
			multiplayer_manager.firebase.player_response_used.connect(self._on_player_response_used)

			# Connect to the submitted_responses_updated signal if it exists
			print("Checking for submitted_responses_updated signal")
			var responses_signal_found = false  # New variable for the second signal check
			for sig in multiplayer_manager.firebase.get_signal_list():
				if sig["name"] == "submitted_responses_updated":
					responses_signal_found = true
					break

			if responses_signal_found:
				print("Signal submitted_responses_updated found, connecting...")
				multiplayer_manager.firebase.submitted_responses_updated.connect(self._on_submitted_responses_updated)
				print("Submitted responses signal connected successfully")
			else:
				print("submitted_responses_updated signal not found, creating it")
				# We'll use the response check timer instead

			# Connect to the judge_index_updated signal if it exists
			print("Checking for judge_index_updated signal")
			var judge_signal_found = false
			for sig in multiplayer_manager.firebase.get_signal_list():
				if sig["name"] == "judge_index_updated":
					judge_signal_found = true
					break

			if judge_signal_found:
				print("Signal judge_index_updated found, connecting...")
				multiplayer_manager.firebase.judge_index_updated.connect(self._on_judge_index_updated)
				print("Judge index signal connected successfully")
			else:
				print("judge_index_updated signal not found")

		# Start a new multiplayer game
		start_multiplayer_game()

	# Log for debugging
	print("Player UI setup skipped - player columns are hidden")

	# Make sure player container is hidden
	if player_container:
		player_container.visible = false

func update_player_hand(player):
	# This function now only updates the internal state
	# We no longer display player hands in the UI

	# We still need to make sure the player has cards in their hand
	# but we don't need to create UI elements for them

	# This function is kept for compatibility with existing code
	# that calls it, but it doesn't update the UI anymore

	# Log for debugging
	print("Updated internal hand state for player: " + player.pname + ", hand size: " + str(player.hand.size()))

func setup_player_ui():
	# This function is kept for compatibility with existing code that calls it
	# Player UI setup has been simplified - we no longer show player columns
	print("setup_player_ui called - player UI setup skipped (player columns are hidden)")
	
	# Make sure player container is hidden
	if player_container:
		player_container.visible = false

func _on_card_selected(player, card, _card_ui):
	# Allow selection during response phase
	if not judge_timer.is_stopped():
		# Play the card
		player.remove_card_from_hand(card)
		played_cards[player] = card

		# Update internal state
		update_player_hand(player)

		# Add the card to the played cards UI
		add_played_card_to_ui(player, card)

		# Check if all players have submitted responses
		check_all_responses_submitted()

func add_played_card_to_ui(player, card):
	# Create a container for the card
	var card_container = CardScene.instantiate()
	card_container.name = player.pname + "_card"

	# Set the card text
	if card_container.has_node("VBoxContainer/CardText"):
		card_container.get_node("VBoxContainer/CardText").text = card.get("response", "Error")

	# Make the card look better
	if card_container.has_node("VBoxContainer"):
		var vbox = card_container.get_node("VBoxContainer")
		vbox.add_theme_constant_override("separation", 10)

		# Add player name label
		var name_label = Label.new()
		name_label.text = player.pname
		name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		name_label.add_theme_font_size_override("font_size", 16)
		vbox.add_child(name_label)

	# Hide any select button if it exists
	if card_container.has_node("VBoxContainer/SelectButton"):
		card_container.get_node("VBoxContainer/SelectButton").visible = false

	# Add to the played cards container
	played_cards_container.add_child(card_container)

	# Store a reference to the player on the card container
	card_container.set_meta("player_ref", player)

# Function to handle displaying submitted responses from Firebase
func _on_submitted_responses_updated(submitted_responses_data: Array):
	print("\n===== SUBMITTED RESPONSES UPDATED =====")
	print("Processing submitted responses from Firebase: ", submitted_responses_data)
	print("Current game status: ", game_status)
	print("should_display_responses: ", should_display_responses)
	print("Number of existing children in played_cards_container: ", played_cards_container.get_children().size())

	# Check if we should display responses based on game state
	if not should_display_responses:
		print("Not displaying responses as should_display_responses is false.")
		return

	if submitted_responses_data == null or submitted_responses_data.is_empty():
		print("No submitted responses to display or data is null.")
		return

	# Check if we already have the same number of responses displayed
	var current_responses_count = submitted_responses_data.size()
	if current_responses_count == last_displayed_responses_count:
		print("Same number of responses already displayed (", current_responses_count, "), skipping update.")
		return

	# Clear existing cards in the container to display the fresh set
	# This ensures that if the function is called multiple times, we don't get duplicate cards
	for child in played_cards_container.get_children():
		child.queue_free()

	# Update the tracking variable
	last_displayed_responses_count = current_responses_count

	print("Displaying " + str(submitted_responses_data.size()) + " responses.")
	for response_data in submitted_responses_data:
		# Ensure the response_data is a dictionary and has the expected keys
		if not typeof(response_data) == TYPE_DICTIONARY:
			printerr("Invalid response data format: expected Dictionary, got ", typeof(response_data), " for data: ", response_data)
			continue
		if not response_data.has("player_name") or not response_data.has("text"):
			printerr("Invalid response data format: missing 'player_name' or 'text'. Data: ", response_data)
			continue

		var player_name = response_data.get("player_name", "Unknown Player")
		var response_text = response_data.get("text", "No response text")

		# Create response text label (no player name)
		var response_label = Label.new()
		response_label.text = response_text
		response_label.name = player_name + "_response_label"  # Unique name for tracking
		response_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		response_label.add_theme_font_size_override("font_size", 16)
		response_label.add_theme_color_override("font_color", Color.WHITE)
		response_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
		response_label.custom_minimum_size = Vector2(200, 0)  # Set minimum width for wrapping

		played_cards_container.add_child(response_label)
		print("REAL-TIME: Added response to UI - '", response_text, "' (", current_responses_count, " total responses now displayed)")

# Check if all players (except the judge) have submitted responses
func check_all_responses_submitted():
	print("Checking if all players have submitted responses...")

	# Skip if we're not in the right game state
	if game_status != "Waiting for Player Responses":
		print("Not in Waiting for Player Responses state, skipping check")
		return

	# Count how many players have submitted responses
	var responses_count = played_cards.size()
	var expected_responses = players.size() - 1  # All players except the judge

	print("Responses received: ", responses_count, "/", expected_responses)

	# If all players have submitted responses, update the game status
	if responses_count >= expected_responses:
		print("\n\nAll players have submitted responses, changing status to Ready for Judging\n\n")

		# Update local game status
		game_status = "Ready for Judging"
		last_processed_status = "Ready for Judging"

		# Update Firebase
		if multiplayer_manager:
			print("Updating game status via multiplayer_manager")
			multiplayer_manager.update_game_status("Ready for Judging")

			# Use direct Firebase update as backup
			if multiplayer_manager.firebase:
				print("Using direct Firebase update as backup")
				multiplayer_manager.firebase.update_game_data("status", "Ready for Judging")

		# Stop the timer
		judge_timer.stop()
		timer_label.text = "All responses received, ready for judging"

# Handle the timer timeout - handles different game states
func _on_judge_timer_timeout():
	print("Timer expired in state: " + game_status)

	# Handle different game states
	match game_status:
		"Waiting for Player Responses":
			print("Response time is up! Moving to next round.")
			timer_label.text = "Response time is up! Moving to next round..."

			# In multiplayer games, we need to move to the next round
			if is_multiplayer_game and multiplayer_manager:
				print("\n\nTIMER EXPIRED: Starting new round\n\n")

				# First, update the status to show responses are done
				print("\n\nUPDATING STATUS TO: Ready for Judging\n\n")

				# Update local game status
				game_status = "Ready for Judging"
				last_processed_status = "Ready for Judging"

				# Update Firebase
				multiplayer_manager.update_game_status("Ready for Judging")

				# Use direct Firebase update as backup
				if multiplayer_manager.firebase:
					print("Using direct Firebase update as backup")
					multiplayer_manager.firebase.update_game_data("status", "Ready for Judging")

				# Wait a moment to let the status update
				await get_tree().create_timer(2.0).timeout

				# Then start a new round by setting status to Ready
				print("\n\nUPDATING STATUS TO: Ready\n\n")

				# Update local game status
				game_status = "Ready"
				last_processed_status = "Ready"

				# Update Firebase
				multiplayer_manager.update_game_status("Ready")

				# Use direct Firebase update as backup
				multiplayer_manager.firebase.update_game_data("status", "Ready")
				return

			# For local games, just move to the next round
			_on_next_round()

		"Round Over":
			print("Judging time is up! Moving to next round.")
			timer_label.text = "Judging time is up! Moving to next round..."

			# In multiplayer games, the web client handles the judging
			if is_multiplayer_game:
				return

			# For local games, just move to the next round
			_on_next_round()

		_:
			print("Timer expired in unhandled state: " + game_status)
			timer_label.text = "Timer expired..."

# Direct handler for game status changes from Firebase
func _on_direct_game_status_changed(new_status):
	print("DIRECT HANDLER CALLED: Game status changed to: ", new_status)

	# Check if this is the same status we just processed
	if new_status == last_processed_status:
		print("WARNING: Already processed this status, ignoring direct call: ", new_status)

		# Special case for Ready state - we want to process it again if we're in Waiting for Players
		if new_status == "Ready" and game_status == "Waiting for Players":
			print("Special case: Processing Ready state even though it was last processed (direct call)")
			# Continue processing
		else:
			return

	# Call the main handler
	handle_game_state_change(new_status)

# Handle game state changes from Firebase
func handle_game_state_change(new_status):
	print("HANDLER CALLED: Game status changed to: ", new_status)

	# Check if we're already processing a state change
	if is_processing_state:
		print("WARNING: Already processing a state change, ignoring new state: ", new_status)
		return

	# Check if this is the same status we just processed
	if new_status == last_processed_status:
		print("WARNING: Already processed this status, ignoring: ", new_status)

		# Special case for Ready state - we want to process it again if we're in Waiting for Players
		if new_status == "Ready" and game_status == "Waiting for Players":
			print("Special case: Processing Ready state even though it was last processed")
			# Continue processing
		else:
			return

	# Set the processing flag
	is_processing_state = true

	# First, make sure we have the latest player data and judge index from Firebase
	if multiplayer_manager and multiplayer_manager.firebase:
		print("Checking for players and judge index in Firebase")

		# Use our dedicated function to update the judge label with the latest data from Firebase
		await update_judge_asks_label()

		# Get the players array from Firebase
		var firebase_players = []
		if multiplayer_manager.players and multiplayer_manager.players.size() > 0:
			firebase_players = multiplayer_manager.players
			print("Found players in multiplayer_manager: ", firebase_players)

		# Process any new players
		for player_name in firebase_players:
			var player_exists = false
			for p in players:
				if p.pname == player_name:
					player_exists = true
					break

			if not player_exists:
				print("Adding player from Firebase: " + player_name)
				var player = Player.new(player_name)
				players.append(player)

				# Update UI (simplified - no longer showing player columns)
				setup_player_ui()

				# Assign responses to this player
				print("Assigning responses to player from Firebase: " + player_name)
				var indices = assign_responses_to_player(player_name)

				# Wait a moment to ensure Firebase is ready
				await get_tree().create_timer(0.2).timeout

				# Update Firebase with the player's response indices
				print("Updating response indices for player: ", player_name)
				multiplayer_manager.update_player_response_indices(player_name, indices)

				# Wait a moment to ensure the indices are saved
				await get_tree().create_timer(0.2).timeout

	# Update local game status and last processed status
	game_status = new_status
	last_processed_status = new_status

	# Handle different game states
	match new_status:
		"Waiting for Prompt":
			print("\n\n==== PROCESSING WAITING FOR PROMPT STATE ====")

			# This is a new state where the web client is waiting for the Godot client to select a prompt
			# Check if we have players
			if players.size() == 0:
				print("ERROR: No players available for Waiting for Prompt state")
				is_processing_state = false
				return
				
			# IMPORTANT: Update the judge label first with the correct judge
			await update_judge_asks_label()
			print("Updated judge label in Waiting for Prompt state")

			# Always force prompt selection in this state regardless of prompt_sent_for_round
			print("Current round: ", current_round, ", prompt_sent_for_round: ", prompt_sent_for_round)

			# Debug the current prompt display state
			if current_prompt_display:
				print("CurrentPromptDisplay before selection - visible: ", current_prompt_display.visible)
				print("CurrentPromptDisplay before selection - text: ", current_prompt_display.text)
			else:
				print("CurrentPromptDisplay is null before prompt selection!")

			# Reset current_prompt and clear any previous prompts
			current_prompt = {}
			if current_prompt_display:
				current_prompt_display.text = ""
				current_prompt_display.visible = false
				print("Cleared current prompt display")

			# Unconditionally select a new prompt
			print("Forcing prompt selection regardless of prompt_sent_for_round value")
			select_and_display_prompt()

			# Wait a short time for prompt to be visible
			await get_tree().create_timer(0.5).timeout

			# Force visibility again after a short delay
			ensure_prompt_visible()

			# Debug the current prompt display state after selection
			if current_prompt_display:
				print("CurrentPromptDisplay after selection - visible: ", current_prompt_display.visible)
				print("CurrentPromptDisplay after selection - text: ", current_prompt_display.text)
			else:
				print("CurrentPromptDisplay is null after prompt selection!")

			# After prompt is selected and visible, change state to Ready
			print("\n\n==== CHANGING GAME STATUS TO: Ready ====")

			# Update Firebase with Ready status
			if multiplayer_manager:
				print("Updating game status via multiplayer_manager to Ready")
				multiplayer_manager.update_game_status("Ready")

				# Use direct Firebase update as backup
				if multiplayer_manager.firebase:
					print("Using direct Firebase update as backup to set status Ready")
					multiplayer_manager.firebase.update_game_data("status", "Ready")

			print("==== END PROCESSING WAITING FOR PROMPT STATE ====\n\n")

		"Ready":
			print("DEBUG: Processing Ready state")

			# Set the flag to not display responses at the start of a new round
			should_display_responses = false
			print("Set should_display_responses to false in Ready state")

			# Reset the response tracking for the new round
			last_displayed_responses_count = 0
			print("Reset response tracking for new round")

			# Clear any existing responses from the previous round
			print("Clearing played cards container in Ready state")
			for child in played_cards_container.get_children():
				child.queue_free()
				
			# IMPORTANT: Update the judge label first before selecting prompt
			await update_judge_asks_label()
			print("Updated judge label in Ready state")

			select_and_display_prompt()

			# Check if we have players
			if players.size() == 0:
				print("ERROR: No players available for Ready state")
				is_processing_state = false
				return

			# IMPORTANT: Always force a new prompt selection for each round
			print("IMPORTANT: Always forcing new prompt selection for round ", current_round)

			# Clear any previous prompts
			if current_prompt_display:
				current_prompt_display.visible = false

			# Clear the current prompt to force a new selection
			current_prompt = {"prompt": ""}
			print("Cleared current_prompt to force new selection")

			# Force a reload of prompts to ensure we get fresh ones
			prompts = load_prompts()
			randomize()
			prompts.shuffle()
			print("Reloaded and shuffled prompts array for new round")

			# Select and display a new prompt
			select_and_display_prompt()

			# Ensure the prompt is visible
			ensure_prompt_visible()

			# Mark that we've sent a prompt for this round
			prompt_sent_for_round = current_round
			print("Updated prompt_sent_for_round = ", current_round)

			# Start a timer to transition to "Waiting for Player Responses" if we're not in the initial game setup
			# This ensures we don't get stuck waiting for the web client to change states
			if current_round > 0:
				print("Starting timer to transition to Waiting for Player Responses")
				await get_tree().create_timer(3.0).timeout

				# Only proceed with the transition if we're still in the Ready state
				if game_status == "Ready":
					print("\n\n==== AUTO-TRANSITIONING TO: Waiting for Player Responses ====")

					# Update local game status
					game_status = "Waiting for Player Responses"
					last_processed_status = "Waiting for Player Responses"

					# Update Firebase
					if multiplayer_manager:
						print("Updating game status via multiplayer_manager to Waiting for Player Responses")
						multiplayer_manager.update_game_status("Waiting for Player Responses")

						# Use direct Firebase update as backup
						if multiplayer_manager.firebase:
							print("Using direct Firebase update as backup")
							multiplayer_manager.firebase.update_game_data("status", "Waiting for Player Responses")

					# Start a timer for player responses (30 seconds)
					timer_label.text = "Players are responding..."
					timer_label.visible = true
					judge_timer.wait_time = 30.0
					judge_timer.start()

					# Set the flag to display responses as they arrive
					should_display_responses = true
					print("Set should_display_responses to true in Waiting for Player Responses state")

					print("==== END AUTO-TRANSITION TO WAITING FOR PLAYER RESPONSES ====\n\n")

		"Waiting for Player Responses":
			print("\n\n==== WAITING FOR PLAYER RESPONSES STATE ====")
			# Start the response timer
			timer_label.text = "Players are responding..."
			timer_label.visible = true
			judge_timer.wait_time = 30.0
			judge_timer.start()

			# Set the flag to display responses as they arrive
			should_display_responses = true
			print("Set should_display_responses to true in Waiting for Player Responses state")

			# Reset the response tracking for the new response phase
			last_displayed_responses_count = 0
			print("Reset response tracking for new response phase")

			# Clear any existing responses from the previous round
			print("Clearing played cards container for new responses")
			for child in played_cards_container.get_children():
				child.queue_free()

		"Ready for Judging":
			print("\n\n==== READY FOR JUDGING STATE ====")
			# Stop the response timer if it's running
			if not judge_timer.is_stopped():
				judge_timer.stop()

			# Update UI
			timer_label.text = "Judging in progress..."

			# Update the prompt label with the current prompt
			if prompt_label and current_prompt and current_prompt.has("prompt"):
				prompt_label.text = "Prompt: " + current_prompt.get("prompt", "")

			# Set the flag to display responses
			should_display_responses = true
			print("Set should_display_responses to true in Ready for Judging state")

			# Note: We're no longer clearing the played cards container here
			# since we want to display responses as they arrive
			print("Keeping existing responses visible in Ready for Judging state")


		"Winner Chosen":
			# Stop the judging timer if it's running
			if not judge_timer.is_stopped():
				judge_timer.stop()

			# Update UI to show we're no longer judging
			timer_label.text = "Winner chosen!"

			# Set the flag to not display responses
			should_display_responses = false
			print("Set should_display_responses to false in Winner Chosen state")

			# Clear the played cards container when a winner is chosen
			print("Clearing played cards container in Winner Chosen state")
			for child in played_cards_container.get_children():
				child.queue_free()

			# Get winner information from Firebase
			if multiplayer_manager:
				print("Getting winner information from Firebase...")
				# These are now async functions, so we need to use await
				var winner_name = await multiplayer_manager.get_game_winner()
				var winning_response = await multiplayer_manager.get_winning_response()

				print("Retrieved winner: ", winner_name)
				print("Retrieved winning response: ", winning_response)

				# Display winner
				display_winner(winner_name, winning_response)

		"Closed":
			# Return to main menu
			get_tree().change_scene_to_file("res://menu.tscn")

	# Reset the processing flag after a short delay
	await get_tree().create_timer(1.0).timeout
	is_processing_state = false
	print("Reset processing flag, ready for next state change")

# Function to load prompts from JSON file with proper structure handling
func load_prompts() -> Array:
	print("\n===== LOADING PROMPTS =====")

	# First try cardGame directory
	var file_path = "res://cardGame/prompts.json"
	print("Attempting to load prompts from: " + file_path)
	var file = FileAccess.open(file_path, FileAccess.READ)

	# If not found, try root directory
	if not file:
		file_path = "res://prompts.json"
		print("First path failed, trying: " + file_path)
		file = FileAccess.open(file_path, FileAccess.READ)

	if file:
		print("File opened successfully")
		var content = file.get_as_text()
		print("File content length: " + str(content.length()))

		var json = JSON.parse_string(content)
		if json != null:
			# Handle different JSON structures
			if typeof(json) == TYPE_ARRAY:
				print("Loaded " + str(json.size()) + " prompts (array format)")

				# Debug: Print first few prompts
				for i in range(min(json.size(), 3)):
					print("Sample prompt " + str(i) + ": " + str(json[i]))

				return json

			elif typeof(json) == TYPE_DICTIONARY and json.has("prompts"):
				print("Loaded " + str(json.prompts.size()) + " prompts (object format)")

				# Debug: Print first few prompts
				for i in range(min(json.prompts.size(), 3)):
					if typeof(json.prompts[i]) == TYPE_DICTIONARY:
						print("Sample prompt " + str(i) + ": " + str(json.prompts[i].get("prompt", "No prompt field")))
					else:
						print("Sample prompt " + str(i) + ": " + str(json.prompts[i]))

				return json.prompts
		else:
			print("ERROR: Failed to parse JSON")
	else:
		print("ERROR: Failed to open prompts file at both locations")

	# Return fallback prompts if loading failed
	print("FALLBACK: Using hardcoded prompts")
	var fallback_prompts = [
		{"prompt": "What's your favorite thing about...?"},
		{"prompt": "Tell us about a time when..."},
		{"prompt": "What would happen if...?"},
		{"prompt": "Who is most likely to...?"},
		{"prompt": "The most awkward thing about ____ is..."}
	]
	return fallback_prompts

# Function to force a new prompt to be selected and displayed
func force_new_prompt():
	print("\n\n==== FORCE NEW PROMPT FUNCTION CALLED ====")

	# Reload the prompts from scratch
	print("Reloading prompts from scratch")
	prompts = load_prompts()

	# Shuffle the prompts thoroughly
	print("Shuffling prompts")
	randomize()
	prompts.shuffle()

	# Get the previous prompt text for comparison
	var previous_prompt_text = ""
	if typeof(current_prompt) == TYPE_DICTIONARY and current_prompt.has("prompt"):
		previous_prompt_text = current_prompt.get("prompt", "")
	print("Previous prompt text: ", previous_prompt_text)

	# Select a random prompt
	var random_index = randi() % prompts.size()
	var new_prompt = prompts[random_index]
	print("Selected new prompt: ", new_prompt)

	# Check if the selected prompt is the same as the previous one
	var selected_prompt_text = ""
	if typeof(new_prompt) == TYPE_DICTIONARY and new_prompt.has("prompt"):
		selected_prompt_text = new_prompt.get("prompt", "")
	else:
		selected_prompt_text = str(new_prompt)

	# If it's the same as the previous prompt, try to select a different one
	if selected_prompt_text == previous_prompt_text and prompts.size() > 1:
		print("WARNING: Selected the same prompt as before, trying again")

		# Try up to 3 times to get a different prompt
		for _attempt in range(3):
			# Get a new random index
			randomize()
			random_index = randi() % prompts.size()
			new_prompt = prompts[random_index]

			# Check if this one is different
			if typeof(new_prompt) == TYPE_DICTIONARY and new_prompt.has("prompt"):
				selected_prompt_text = new_prompt.get("prompt", "")
			else:
				selected_prompt_text = str(new_prompt)

			if selected_prompt_text != previous_prompt_text:
				print("Found a different prompt on attempt ", _attempt + 1)
				break

		print("Final selected prompt: ", new_prompt)

	# Set it as the current prompt
	current_prompt = new_prompt
	print("Set current_prompt to new prompt")

	# Update the display
	if current_prompt_display:
		print("Updating current_prompt_display")
		current_prompt_display.text = new_prompt.get("prompt", "")
		current_prompt_display.visible = true
		print("Set current_prompt_display.text to: ", new_prompt.get("prompt", ""))
		print("Set current_prompt_display.visible = true")
	else:
		print("ERROR: current_prompt_display is null!")

		# Try to find it by node path
		current_prompt_display = get_node_or_null("CurrentPromptDisplay")
		if current_prompt_display:
			print("Found CurrentPromptDisplay by direct path")
			current_prompt_display.text = new_prompt.get("prompt", "")
			current_prompt_display.visible = true
			print("Set CurrentPromptDisplay.text and visible=true")
		else:
			print("CRITICAL ERROR: Could not find CurrentPromptDisplay node!")

	# Send to Firebase
	if multiplayer_manager and multiplayer_manager.firebase:
		print("Sending new prompt to Firebase")

		# Create a simple prompt object with just the text
		var firebase_prompt = {"prompt": new_prompt.get("prompt", "")}
		print("Firebase prompt object: ", firebase_prompt)

		# Use the multiplayer_manager to update the prompt
		multiplayer_manager.update_current_prompt(firebase_prompt)
		print("New prompt sent to Firebase via multiplayer_manager")

		# Double-check with a direct update as a backup
		multiplayer_manager.firebase.update_game_data("current_prompt", firebase_prompt)
		print("New prompt sent to Firebase via direct update (backup)")
	else:
		print("ERROR: multiplayer_manager or firebase not available")

	# Mark that we've sent a prompt for this round
	prompt_sent_for_round = current_round
	print("Updated prompt_sent_for_round = ", current_round)

	print("==== FORCE NEW PROMPT COMPLETE ====\n\n")

# The prompt selection function that ensures each prompt is truly random for each round
func select_and_display_prompt():
	print("\n\n==== SELECTING AND DISPLAYING PROMPT ====")
	print("Current round: ", current_round)
	print("Current prompt_sent_for_round: ", prompt_sent_for_round)
	print("Previous prompt: ", current_prompt.get("prompt", "None"))

	# CRITICAL: Always reload prompts from scratch for each selection to ensure variety
	print("Reloading prompts from scratch for fresh selection")
	prompts = load_prompts() # Reload prompts completely
	used_prompt_indices = []  # Reset used indices


	# Print the current prompt before selection for debugging
	print("Current prompt before selection: ", current_prompt)

	# Thoroughly shuffle the prompts with multiple randomization techniques
	print("Thoroughly shuffling prompts array")
	randomize()  # Reset the random number generator

	# Use multiple sources of randomness
	var time_seed = Time.get_ticks_msec()
	var unix_time = Time.get_unix_time_from_system()
	seed((randi() % 10000) + time_seed + int(unix_time))
	prompts.shuffle()

	# Shuffle again with a different seed based on round number
	seed(time_seed * (current_round + 1) + unix_time)
	prompts.shuffle()

	# Shuffle a third time with yet another seed
	seed(unix_time * (current_round * 3) + time_seed)
	prompts.shuffle()

	print("Prompts array triple-shuffled with round-specific seeds")

	# Debug: Print the entire prompts array so we can see what's available
	print("Current prompts array size: ", prompts.size())
	print("FIRST 3 AVAILABLE PROMPTS:")
	for i in range(min(3, prompts.size())):
		if typeof(prompts[i]) == TYPE_DICTIONARY:
			print("  Prompt " + str(i) + ": " + str(prompts[i].get("prompt", "No prompt field")))
		else:
			print("  Prompt " + str(i) + ": " + str(prompts[i]))

	# Step 1: Select a truly random prompt
	var selected_prompt = null
	var selected_index = -1
	var previous_prompt_text = current_prompt.get("prompt", "")

	if prompts.size() > 0:
		# Create a new random number between 0 and prompts.size()-1
		randomize()  # Reset the random number generator

		# Use a combination of random factors including the current round
		var now = Time.get_unix_time_from_system()
		var random_value = randi() + int(now) + Time.get_ticks_msec() + (current_round * 17)
		selected_index = random_value % prompts.size()

		print("Using randomly selected index: ", selected_index)
		selected_prompt = prompts[selected_index]
		print("Selected prompt at index ", selected_index, ": ", selected_prompt)

		# Check if the selected prompt is the same as the previous one
		var selected_prompt_text = ""
		if typeof(selected_prompt) == TYPE_DICTIONARY and selected_prompt.has("prompt"):
			selected_prompt_text = selected_prompt.get("prompt")
		else:
			selected_prompt_text = str(selected_prompt)

		# If it's the same as the previous prompt, try to select a different one
		if selected_prompt_text == previous_prompt_text and prompts.size() > 1:
			print("WARNING: Selected the same prompt as before, trying again")

			# Try up to 3 times to get a different prompt
			for _attempt in range(3):
				# Get a new random index
				randomize()
				random_value = randi() + int(now) + Time.get_ticks_msec() + (current_round * 23 + _attempt * 7)
				selected_index = random_value % prompts.size()

				selected_prompt = prompts[selected_index]

				# Check if this one is different
				if typeof(selected_prompt) == TYPE_DICTIONARY and selected_prompt.has("prompt"):
					selected_prompt_text = selected_prompt.get("prompt")
				else:
					selected_prompt_text = str(selected_prompt)

				if selected_prompt_text != previous_prompt_text:
					print("Found a different prompt on attempt ", _attempt + 1)
					break

			print("Final selected prompt: ", selected_prompt)

		# Remove the selected prompt from the array to prevent reuse
		prompts.remove_at(selected_index)
		print("Removed selected prompt from array to prevent reuse")

		# Reshuffle the remaining prompts
		prompts.shuffle()
		print("Reshuffled remaining prompts")
	else:
		print("ERROR: No prompts available!")
		# Create a fallback prompt as last resort
		selected_prompt = {"prompt": "Round " + str(current_round) + " - Default prompt"}

	# Step 2: Format the prompt consistently
	if typeof(selected_prompt) == TYPE_DICTIONARY:
		if selected_prompt.has("prompt"):
			current_prompt = {"prompt": selected_prompt.get("prompt")}
			print("Using 'prompt' field: ", current_prompt.get("prompt"))
		else:
			# Handle different prompt formats
			var found_string = false
			for key in selected_prompt:
				if typeof(selected_prompt[key]) == TYPE_STRING:
					current_prompt = {"prompt": selected_prompt[key]}
					print("Using '" + key + "' field as prompt: ", current_prompt.get("prompt"))
					found_string = true
					break
			# If no string value found, use the entire object
			if not found_string:
				current_prompt = {"prompt": str(selected_prompt)}
				print("Using stringified object as prompt: ", current_prompt.get("prompt"))
	else:
		# Not a dictionary, use as is
		current_prompt = {"prompt": str(selected_prompt)}
		print("Using non-dictionary value as prompt: ", current_prompt.get("prompt"))

	print("Final formatted prompt: ", current_prompt)

	# Step 3: Update the judge label with the current judge's name
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		# Get the current judge index directly from Firebase using our dedicated function
		await update_judge_asks_label()
		print("Judge asks label updated in select_and_display_prompt")

	# Step 4: Immediately display on Godot client
	ensure_prompt_visible()

	# Step 5: Mark that we've sent a prompt for this round
	prompt_sent_for_round = current_round
	print("Updated prompt_sent_for_round = ", current_round)

	# Step 5: Wait 2 seconds before sending to Firebase (for web client)
	print("Waiting 2 seconds before uploading to Firebase...")
	await get_tree().create_timer(2.0).timeout

	# Step 6: Upload to Firebase for web client
	if multiplayer_manager and multiplayer_manager.firebase:
		print("Uploading prompt to Firebase for web client")

		# Create a simple prompt object with safe access
		var firebase_prompt = {"prompt": current_prompt.get("prompt", "No prompt available")}

		# Send directly to Firebase
		multiplayer_manager.firebase.update_game_data("current_prompt", firebase_prompt)
		print("Prompt uploaded to Firebase: ", firebase_prompt.get("prompt"))

		# Make sure prompt is still visible after Firebase upload
		ensure_prompt_visible()

	print("==== PROMPT SELECTION COMPLETE ====\n\n")

# Function to load responses from JSON file with proper structure handling
func load_responses() -> Array:
	print("Loading responses...")
	var file = FileAccess.open("res://cardGame/responses.json", FileAccess.READ)
	if not file:
		file = FileAccess.open("res://responses.json", FileAccess.READ)

	if file:
		var content = file.get_as_text()
		var json = JSON.parse_string(content)

		if json != null:
			# Handle different JSON structures
			if typeof(json) == TYPE_ARRAY:
				print("Loaded", json.size(), "responses (array format)")
				return json
			elif typeof(json) == TYPE_DICTIONARY and json.has("responses"):
				# Check if we need to convert "text" fields to "response"
				var response_array = json.responses
				var converted_responses = []

				for resp in response_array:
					if resp.has("text") and not resp.has("response"):
						var new_resp = resp.duplicate()
						new_resp["response"] = resp.text
						converted_responses.append(new_resp)
					else:
						converted_responses.append(resp)

				print("Loaded and converted", converted_responses.size(), "responses (object format)")
				return converted_responses

	# Return fallback responses if loading failed
	print("Failed to load responses, using fallbacks")
	return [
		{"response": "A suspicious stain."},
		{"response": "A lifetime of sadness."},
		{"response": "An awkward silence."},
		{"response": "The inevitable heat death of the universe."}
	]

# Add a key handler for testing the prompt selection directly
func _input(event):
	# Test key for simulating a Ready status change
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_R:
			print("\n\nMANUAL TEST: Simulating Ready status change\n\n")
			handle_game_state_change("Ready")
		elif event.keycode == KEY_J:
			print("\n\nMANUAL TEST: Simulating Ready for Judging status change\n\n")
			handle_game_state_change("Ready for Judging")
		elif event.keycode == KEY_W:
			print("\n\nMANUAL TEST: Simulating Winner Chosen status change\n\n")
			handle_game_state_change("Winner Chosen")
		elif event.keycode == KEY_P:
			print("\n\nMANUAL TEST: Directly testing prompt selection\n\n")
			select_and_display_prompt()
		elif event.keycode == KEY_N:
			print("\n\nMANUAL TEST: Force new prompt\n\n")
			force_new_prompt()
		elif event.keycode == KEY_T:
			print("\n\nMANUAL TEST: Simulating Waiting for Prompt status change\n\n")
			handle_game_state_change("Waiting for Prompt")
		elif event.keycode == KEY_D:
			# Add current player and prompt debug info
			print("\n\nDEBUG INFO:")
			print("Current prompt: ", current_prompt)
			print("Current round: ", current_round)
			print("Prompt sent for round: ", prompt_sent_for_round)
			print("Game status: ", game_status)
			if current_prompt_display:
				print("Current prompt display visible: ", current_prompt_display.visible)
				print("Current prompt display text: ", current_prompt_display.text)
			else:
				print("Current prompt display not found")
			print("\n")
		elif event.keycode == KEY_J:
			# Manual judge index debug
			print("\n\nMANUAL JUDGE INDEX DEBUG:")
			if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
				if multiplayer_manager.firebase.has_method("debug_get_entire_game_data"):
					print("Getting entire game data...")
					await multiplayer_manager.firebase.debug_get_entire_game_data()
				if multiplayer_manager.firebase.has_method("get_current_judge_index"):
					print("Getting current judge index...")
					var judge_index = await multiplayer_manager.firebase.get_current_judge_index()
					print("Current judge index from Firebase: ", judge_index)
					print("Local current_judge_index: ", current_judge_index)
					var player_names = []
					for p in players:
						player_names.append(p.pname)
					print("Players array: ", player_names)
			print("END JUDGE INDEX DEBUG\n\n")
		elif event.keycode == KEY_R:
			# Manual response check debug
			print("\n\nMANUAL RESPONSE CHECK DEBUG:")
			print("Current game status: ", game_status)
			print("should_display_responses: ", should_display_responses)
			if is_multiplayer_game and multiplayer_manager:
				print("Manually checking for new responses...")
				await _check_for_new_responses()
			print("END RESPONSE CHECK DEBUG\n\n")

# Add the missing function that updates the timer display
func _update_timer_display():
	# Update the timer display based on the current game state
	if judge_timer.is_stopped():
		# Hide the timer label when the timer is not running
		if timer_label:
			timer_label.visible = false
		return

	# Make sure the timer label is visible when the timer is running
	if timer_label and not timer_label.visible:
		timer_label.visible = true

	# Calculate remaining time
	var time_left = int(judge_timer.time_left)

	# Only update the UI if the time has changed
	if time_left == last_timer_update:
		return

	# Store the current time
	last_timer_update = time_left

	# Update display based on game state
	match game_status:
		"Waiting for Player Responses":
			timer_label.text = "Players are responding... " + str(time_left) + " seconds"
		"Round Over":
			timer_label.text = "Waiting for judging... " + str(time_left) + " seconds"
		_:
			timer_label.text = "Time remaining: " + str(time_left) + " seconds"

# Function to check for new responses from Firebase
func _check_for_new_responses():
	# Only check if we're in a multiplayer game with Firebase
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		# Get submitted responses from Firebase (using await since this is an async function)
		var submitted_responses = await multiplayer_manager.get_submitted_responses()

		# Update the should_display_responses flag based on game state
		should_display_responses = (game_status == "Waiting for Player Responses" or game_status == "Ready for Judging")

		if submitted_responses and submitted_responses.size() > 0:
			# Only display responses if we're in the right game state
			if should_display_responses:
				# Check if this is a new response count
				if submitted_responses.size() != last_displayed_responses_count:
					print("NEW RESPONSES DETECTED: Found " + str(submitted_responses.size()) + " responses (was " + str(last_displayed_responses_count) + ")")
					# Process the responses
					_on_submitted_responses_updated(submitted_responses)
					print("Displayed " + str(submitted_responses.size()) + " responses in Firebase check")
				# else: Same number of responses, no need to update
			else:
				# Just log that we found responses but won't display them
				print("Found " + str(submitted_responses.size()) + " responses but not displaying them (game state: " + game_status + ")")

# Multiplayer functions
func start_multiplayer_game():
	# Clear any existing game state
	players = []
	played_cards = {}

	# Start waiting for players
	waiting_for_players = true
	game_status = "Waiting for Players"

	# Update UI to show waiting state
	if prompt_label:
		prompt_label.text = ""  # Clear any previous text
	if judge_label:
		judge_label.text = ""   # Clear any previous text

	# Hide the current prompt display until we have a prompt
	if current_prompt_display:
		current_prompt_display.visible = false

	# Hide the winning response display
	if winning_response_display:
		winning_response_display.visible = false

	# Show the room code display
	if room_code_display:
		print("cardGame: Making room_code_display visible")
		room_code_display.visible = true

	# Connect to the game_created signal if not already connected
	if multiplayer_manager and not multiplayer_manager.game_created.is_connected(self.show_room_code):
		multiplayer_manager.game_created.connect(self.show_room_code)

	# Start the multiplayer game
	if multiplayer_manager:
		multiplayer_manager.start_new_game()

		# Update game status in Firebase
		print("\n\nSetting initial game status to: Waiting for Players\n\n")
		multiplayer_manager.update_game_status(game_status)

		# Use direct Firebase update as backup
		if multiplayer_manager.firebase:
			print("Using direct Firebase update as backup")
			multiplayer_manager.firebase.update_game_data("status", "Waiting for Players")

	# Load prompts and responses
	prompts = load_prompts()
	responses = load_responses()

	# Debug information
	print("Loaded prompts: ", prompts.size())
	print("Loaded responses: ", responses.size())

	# Shuffle the cards
	randomize()
	prompts.shuffle()
	responses.shuffle()

# Initialize players with given names
func initialize_players(player_names: Array):
	players = []  # Clear any existing players
	for pname in player_names:
		var player = Player.new(pname)  # Create new player with name
		players.append(player)
		distribute_cards(player)  # Give them cards
		print("Player", player.pname, "has", player.hand.size(), "cards")

# Distribute cards to a player
func distribute_cards(player):
	for i in range(5):  # Give each player 5 cards
		if responses.size() > 0:
			var card = responses.pop_back()
			player.add_card_to_hand(card)
		else:
			print("WARNING: Ran out of response cards!")

func start_game():
	# Hide the room code display if it's visible
	if room_code_display:
		room_code_display.visible = false

	# Set initial game state
	current_round = 1

	# Set initial game status
	# Only set game status if we're not in a multiplayer game
	# In multiplayer games, the status is controlled by Firebase
	if not is_multiplayer_game:
		game_status = "Round 1"

	# Reset judge-related variables
	current_judge_index = 0

	# Update game status in Firebase
	if is_multiplayer_game and multiplayer_manager:
		multiplayer_manager.update_game_status(game_status)
		multiplayer_manager.update_current_round(current_round)

	# Initial round
	start_round()

# Start a new round of the game
func start_round():
	print("\n\n==== STARTING ROUND " + str(current_round) + " ====")

	# Check if UI elements exist
	if not prompt_label or not judge_label or not timer_label or not played_cards_container:
		push_error("UI elements not found. Cannot start round.")
		return

	# Check if players array is valid
	if players.size() == 0:
		push_error("No players available")
		return

	# Print judge information at the start of each round
	print("\n===== ROUND START JUDGE INFO =====")
	print("Current judge index: ", current_judge_index)

	# Print all players for debugging
	print("All players in array:")
	for i in range(players.size()):
		print("  Player ", i, ": ", players[i].pname)

	# Get the current judge's name
	if players.size() > current_judge_index:
		var judge_name = players[current_judge_index].pname
		print("Current judge at start of round: ", judge_name, " at index ", current_judge_index)
	else:
		print("ERROR: Invalid judge index at start of round: ", current_judge_index)

	# If this is a multiplayer game, try to get the judge index from Firebase
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		print("Attempting to get current judge index from Firebase at start of round...")
		if multiplayer_manager.firebase.has_method("get_current_judge_index"):
			print("About to call get_current_judge_index() at start of round...")
			var firebase_judge_index_raw = await multiplayer_manager.firebase.get_current_judge_index()
			print("Raw Firebase judge index at start of round: ", firebase_judge_index_raw, " (type: ", typeof(firebase_judge_index_raw), ")")

			# Force conversion to int if it's a float
			var firebase_judge_index = firebase_judge_index_raw
			if firebase_judge_index_raw is float:
				firebase_judge_index = int(firebase_judge_index_raw)
				print("Manually converted float to int at start of round: ", firebase_judge_index)

			print("Final Firebase judge index at start of round: ", firebase_judge_index)

			# Print all players for debugging
			print("All players in array:")
			for i in range(players.size()):
				print("  Player ", i, ": ", players[i].pname)

			# IMPORTANT: Force the judge index to be the one from Firebase
			# Use the Firebase value directly
			if firebase_judge_index != null and firebase_judge_index >= 0 and players.size() > firebase_judge_index:
				var judge_name = players[firebase_judge_index].pname
				print("Current judge from Firebase at start of round is: ", judge_name, " at index ", firebase_judge_index)

				# Update the JudgeAsksLabel directly with the Firebase judge
				if not judge_asks_label:
					judge_asks_label = get_node_or_null("JudgeAsksLabel")

				if judge_asks_label:
					judge_asks_label.text = judge_name + " asks:"
					judge_asks_label.visible = true
					print("DIRECTLY updated JudgeAsksLabel at start of round to: ", judge_asks_label.text)
				else:
					print("ERROR: Could not find JudgeAsksLabel at start of round")

				# Now update the current_judge_index for future use
				if firebase_judge_index != current_judge_index:
					print("MISMATCH: Firebase judge index differs from local judge index!")
					current_judge_index = firebase_judge_index
					print("Updated current_judge_index to match Firebase: ", current_judge_index)
			else:
				print("ERROR: Invalid Firebase judge index at start of round: ", firebase_judge_index, " for players array of size ", players.size())
				# Try with index 1 as fallback (since you mentioned index 0 is wrong)
				if players.size() > 1:
					var judge_name = players[1].pname
					print("FALLBACK: Using player at index 1 as judge at start of round: ", judge_name)
					if judge_asks_label:
						judge_asks_label.text = judge_name + " asks:"
						judge_asks_label.visible = true
						print("Updated JudgeAsksLabel with fallback to: ", judge_asks_label.text)
				# If index 1 doesn't work, try index 0
				elif players.size() > 0:
					var judge_name = players[0].pname
					print("FALLBACK: Using player at index 0 as judge: ", judge_name)
					if judge_asks_label:
						judge_asks_label.text = judge_name + " asks:"
						judge_asks_label.visible = true
						print("Updated JudgeAsksLabel with fallback to: ", judge_asks_label.text)
			print("===== END JUDGE DEBUG INFO =====\n")

	# Reset played cards for this round
	played_cards = {}

	# IMPORTANT: Reset the prompt sent flag for the new round
	# This ensures a new prompt will be selected for this round
	prompt_sent_for_round = 0
	print("Reset prompt_sent_for_round to 0 for new round " + str(current_round))

	# IMPORTANT: Clear the current prompt to force a new selection
	current_prompt = {"prompt": ""}
	print("Cleared current_prompt to force new selection")

	# Update game status
	game_status = "Round " + str(current_round)

	# Clear the played cards container
	for child in played_cards_container.get_children():
		child.queue_free()

	# If this is a multiplayer game, wait for the "Ready" status from Firebase
	if is_multiplayer_game and multiplayer_manager:
		# Update Firebase with current round
		multiplayer_manager.update_game_status("Waiting for Players")
		multiplayer_manager.update_current_round(current_round)
		print("Updated Firebase with current_round = " + str(current_round))

		# Wait for the "Ready" status
		prompt_label.text = ""

		# Hide the current prompt display until we have a new prompt
		if current_prompt_display:
			current_prompt_display.visible = false
			print("Hidden current_prompt_display until new prompt is selected")

		# No longer display the room code in the judge_label
		judge_label.text = ""

		# Force a reload of prompts to ensure we get fresh ones
		prompts = load_prompts()
		randomize()
		prompts.shuffle()
		print("Reloaded and shuffled prompts array for new round")

		print("==== END STARTING ROUND " + str(current_round) + " ====\n\n")
		return

	# For local games, continue with the round setup

	# Always select a new prompt for each round
	# Check if we need to reload prompts (only if they're depleted)
	if prompts.size() == 0:
		print("Prompts array depleted, reloading prompts")
		prompts = load_prompts()
		randomize()
		prompts.shuffle()

	# Always select and display a new prompt
	print("Forcing new prompt selection for round " + str(current_round))
	select_and_display_prompt()

	# Mark that we've sent a prompt for this round
	prompt_sent_for_round = current_round

	# Update UI
	judge_label.text = "Round: " + str(current_round)

	# Deal new cards to all players
	for player in players:
		if player.hand.size() < 5:
			# Deal cards to all players
			while player.hand.size() < 5 and responses.size() > 0:
				var card = responses.pop_back()
				player.add_card_to_hand(card)

			# Update hand display
			update_player_hand(player)

	# Update game status to waiting for responses
	if not is_multiplayer_game:
		game_status = "Waiting for Player Responses"

		# Start response phase
		timer_label.text = "Players are responding..."

		# Set a timer for response phase (30 seconds)
		judge_timer.wait_time = 30.0
		judge_timer.start()

func _on_next_round():
	print("\n\n==== NEXT ROUND FUNCTION CALLED ====")

	# Clear played cards container
	for child in played_cards_container.get_children():
		child.queue_free()

	# Hide the winning response display
	if winning_response_display:
		winning_response_display.visible = false
		
	# Use our new centralized function to start a new round
	start_new_round()

	# Check for game end
	for player in players:
		if player.score >= 7: # 7 wins needed
			print("Player " + player.pname + " has reached 7 points, showing game over")
			show_game_over(player)
			return

	# Update game status to Round Over
	game_status = "Round Over"
	if is_multiplayer_game and multiplayer_manager:
		multiplayer_manager.update_game_status(game_status)
		print("Updated game status to Round Over")

		# Wait 5 seconds before starting new round
		print("Waiting 5 seconds before starting new round")
		await get_tree().create_timer(5.0).timeout

	# Increment round counter
	current_round += 1
	print("Incremented current_round to " + str(current_round))

	# IMPORTANT: Reset the prompt sent flag for the new round
	# This ensures a new prompt will be selected for this round
	prompt_sent_for_round = 0
	print("Reset prompt_sent_for_round to 0 for new round " + str(current_round))

	# IMPORTANT: Clear the current prompt to force a new selection
	current_prompt = {"prompt": ""}
	print("Cleared current_prompt to force new selection")

	# Force a reload of prompts to ensure we get fresh ones
	prompts = load_prompts()
	randomize()
	prompts.shuffle()
	print("Reloaded and shuffled prompts array for new round")

	# Start new round
	start_round()

	print("==== END NEXT ROUND FUNCTION ====\n\n")

func show_game_over(winner_player):
	# Clear the UI
	for child in played_cards_container.get_children():
		child.queue_free()

	# Hide the winning response display
	if winning_response_display:
		winning_response_display.visible = false

	# Update game status to Game Over
	game_status = "Game Over"
	if is_multiplayer_game and multiplayer_manager:
		multiplayer_manager.update_game_status(game_status)
		multiplayer_manager.update_game_winner(winner_player.pname)

	# Create game over container
	var game_over_container = VBoxContainer.new()
	game_over_container.name = "game_over_display"
	game_over_container.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	game_over_container.size_flags_vertical = Control.SIZE_EXPAND_FILL
	game_over_container.alignment = 1 # Center

	# Create game over label
	var game_over_label = Label.new()
	game_over_label.text = "GAME OVER"
	game_over_label.horizontal_alignment = 1 # Center
	game_over_label.add_theme_font_size_override("font_size", 48)
	game_over_container.add_child(game_over_label)

	# Create winner label
	var winner_label = Label.new()
	winner_label.text = winner_player.pname + " WINS THE GAME!"
	winner_label.horizontal_alignment = 1 # Center
	winner_label.add_theme_font_size_override("font_size", 32)
	game_over_container.add_child(winner_label)

	# Create score label
	var score_label = Label.new()
	score_label.text = "Final Score: " + str(winner_player.score) + " points"
	score_label.horizontal_alignment = 1 # Center
	game_over_container.add_child(score_label)

	# Add restart button
	var restart_button = Button.new()
	restart_button.text = "Play Again"
	restart_button.pressed.connect(get_tree().reload_current_scene)
	game_over_container.add_child(restart_button)

	# Add to the UI
	played_cards_container.add_child(game_over_container)

	# Update prompt and judge labels
	prompt_label.text = "Game Over"
	judge_label.text = "Waiting for leader to start a new game"

func display_winner(winner_name, winning_response):
	print("\n\n==== DISPLAY WINNER FUNCTION CALLED ====")
	print("Winner: ", winner_name, " with response: ", winning_response)
	print("Current round: ", current_round)
	print("Current prompt_sent_for_round: ", prompt_sent_for_round)
	print("Current prompt: ", current_prompt)

	# Call our highlight function to highlight the winning response
	highlight_winning_response(winner_name, winning_response)

	# Wait 6 seconds before moving to the next round
	print("Waiting 6 seconds before moving to the next round...")
	await get_tree().create_timer(6.0).timeout

	# Hide the winning response display when moving to the next round
	if winning_response_display:
		winning_response_display.visible = false

	# If we're still in the Winner Chosen state, move to Waiting for Prompt
	# Changed from Ready to Waiting for Prompt to ensure Godot client always selects the prompt
	if game_status == "Winner Chosen" and multiplayer_manager:
		print("\n\n==== TRANSITIONING TO WAITING FOR PROMPT FOR NEXT ROUND ====")

		# IMPORTANT: First increment the round counter
		current_round += 1
		print("Incremented current_round to: ", current_round)

		# IMPORTANT: Reset the prompt sent flag to ensure a new prompt is selected
		prompt_sent_for_round = 0
		print("Reset prompt_sent_for_round to 0")

		# IMPORTANT: Clear the current prompt to force a new selection
		current_prompt = {"prompt": ""}
		print("Cleared current_prompt to force new selection")

		# Hide the current prompt display until we have a new prompt
		if current_prompt_display:
			current_prompt_display.visible = false
			print("Hidden current_prompt_display until new prompt is selected")

		# Update the current round in Firebase
		multiplayer_manager.update_current_round(current_round)
		print("Updated current_round in Firebase to: ", current_round)

		# Force a reload of prompts to ensure we get fresh ones
		prompts = load_prompts()
		randomize()
		prompts.shuffle()
		print("Reloaded and shuffled prompts array for new round")

		# Update game status to Waiting for Prompt
		multiplayer_manager.update_game_status("Waiting for Prompt")
		print("Updated game status to: Waiting for Prompt")

		# Wait a moment to ensure the status change is processed
		await get_tree().create_timer(0.5).timeout

		# Force a direct prompt selection as a backup
		print("Forcing direct prompt selection as backup")
		force_new_prompt()

		# Double-check that the prompt is different from the previous round
		print("Double-checking that the prompt is different from previous rounds")
		if current_prompt.get("prompt", "") == "":
			print("WARNING: Prompt is still empty after force_new_prompt, trying again")
			await get_tree().create_timer(0.5).timeout
			force_new_prompt()

		print("==== END TRANSITIONING TO WAITING FOR PROMPT ====\n\n")

func highlight_winning_response(winner_name, winning_response_text):
	print("Highlighting winning response: ", winning_response_text, " by player: ", winner_name)

	# Clear any existing cards in the played cards container
	for child in played_cards_container.get_children():
		child.queue_free()

	# Make sure we have a reference to the winning response display
	if not winning_response_display:
		winning_response_display = $WinningResponseDisplay
		if not winning_response_display:
			print("ERROR: Could not find WinningResponseDisplay node")
			return

	# Update the winning response display with the winner information
	if winning_response_display.has_node("VBoxContainer/PlayerNameLabel"):
		winning_response_display.get_node("VBoxContainer/PlayerNameLabel").text = winner_name

	if winning_response_display.has_node("VBoxContainer/ResponseText"):
		winning_response_display.get_node("VBoxContainer/ResponseText").text = winning_response_text

	# Apply a stylish background to the panel
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0.0, 0.0, 0.0, 0.7)
	style_box.corner_radius_top_left = 15
	style_box.corner_radius_top_right = 15
	style_box.corner_radius_bottom_left = 15
	style_box.corner_radius_bottom_right = 15
	style_box.border_width_bottom = 4
	style_box.border_width_top = 4
	style_box.border_width_left = 4
	style_box.border_width_right = 4
	style_box.border_color = Color(0.8, 0.8, 0.8, 1.0)
	winning_response_display.add_theme_stylebox_override("panel", style_box)

	# Make the winning response display visible
	winning_response_display.visible = true

	print("Winning response displayed successfully in dedicated panel")

# Function to assign response cards to a player and return the indices
func assign_responses_to_player(player_name: String) -> Array:
	print("Assigning responses to player: " + player_name)

	# Find the player with the given name
	var player = null
	for p in players:
		if p.pname == player_name:
			player = p
			break

	if not player:
		print("ERROR: Player not found with name: " + player_name)
		return []

	# Create arrays to track assigned responses and indices
	var assigned_indices = []
	var assigned_responses = []
	var assigned_response_texts = []

	# Assign 5 response cards to the player
	var responses_needed = 5
	print("Player needs " + str(responses_needed) + " response cards")

	# Make sure we have enough responses available
	if responses.size() < responses_needed:
		print("WARNING: Not enough responses available. Available: " + str(responses.size()) + ", Needed: " + str(responses_needed))
		responses_needed = responses.size()

	# Assign responses to the player
	for i in range(responses_needed):
		if responses.size() > 0:
			var response_card = responses.pop_back()
			player.add_card_to_hand(response_card)
			assigned_responses.append(response_card)
			assigned_indices.append(i)  # Use sequential indices
			
			# Store the response text for logging
			var response_text = ""
			if response_card.has("response"):
				response_text = response_card.response
			elif response_card.has("text"):
				response_text = response_card.text
			assigned_response_texts.append(response_text)
			
			print("Assigned response " + str(i) + ": " + response_text)

	print("Player " + player_name + " now has " + str(assigned_indices.size()) + " responses assigned")
	return assigned_indices

# Handler for when a player joins the game
func on_player_joined(player_name: String):
	print("Player joined the game: ", player_name)

	# Check if player already exists in our local players array
	var player_exists = false
	for p in players:
		if p.pname == player_name:
			player_exists = true
			break

	# Add player if they don't exist
	if not player_exists:
		print("Adding new player to local players array: ", player_name)
		var player = Player.new(player_name)
		players.append(player)

		# Distribute cards to the new player if we have responses available
		distribute_cards(player)

		# Update UI (simplified - no longer showing player columns)
		setup_player_ui()

		print("Player ", player_name, " added successfully with ", player.hand.size(), " cards")
	else:
		print("Player ", player_name, " already exists in local players array")

# Handler for when a player selects an answer
func on_player_answer_selected(player_name: String, response: String):
	print("Player ", player_name, " selected answer: ", response)

	# Find the player in our local array
	var player = null
	for p in players:
		if p.pname == player_name:
			player = p
			break

	if player:
		# Handle the player's answer selection
		# This could involve updating UI, tracking responses, etc.
		print("Processing answer selection for player: ", player_name)

		# Add the response to played cards if we're in the response phase
		if game_status == "Waiting for Player Responses":
			# Create a response object similar to how cards are structured
			var response_card = {"response": response}
			played_cards[player] = response_card

			# Add to UI
			add_played_card_to_ui(player, response_card)

			# Check if all players have submitted
			check_all_responses_submitted()
	else:
		print("WARNING: Player ", player_name, " not found in local players array")

# Handler for judge index updates from Firebase
func _on_judge_index_updated(new_judge_index: int):
	print("JUDGE INDEX UPDATE: Received judge index update from Firebase: ", new_judge_index)

	# Update the local judge index
	current_judge_index = new_judge_index
	print("Updated local current_judge_index to: ", current_judge_index)

	# Update the judge asks label immediately
	await update_judge_asks_label()
	print("Judge asks label updated due to judge index change from Firebase")

# Get response text by index for a specific player
func get_response_by_index(player_name: String, index: int) -> String:
	print("Getting response by index for player: ", player_name, ", index: ", index)

	# Find the player in our local array
	var player = null
	for p in players:
		if p.pname == player_name:
			player = p
			break

	if player and player.hand.size() > index and index >= 0:
		var response_card = player.hand[index]
		if typeof(response_card) == TYPE_DICTIONARY and response_card.has("response"):
			return response_card.get("response", "Unknown response")
		else:
			return str(response_card)
	else:
		print("WARNING: Invalid index ", index, " for player ", player_name, " or player not found")
		return "Unknown response"

# Handler for database errors (optional - called by multiplayer manager if it exists)
func on_database_error(error_code: int, error_message: String):
	print("Database error received: Code ", error_code, " - ", error_message)
	# Handle database errors here if needed
	# For now, just log the error

# Handler for player responses used in Firebase
func _on_player_response_used(player_name: String, response_text: String):
	print("\n===== PLAYER RESPONSE USED =====")
	print("Response used by player: ", player_name, " - ", response_text)

	# Find player with matching name
	var player = null
	for p in players:
		if p.pname == player_name:
			player = p
			break

	if player:
		print("Found player in players array")
		# Print current hand for debugging
		print("Current hand before removal:")
		for i in range(player.hand.size()):
			var card = player.hand[i]
			var card_text = ""
			if card.has("response"):
				card_text = card.response
			elif card.has("text"):
				card_text = card.text
			print("  Card " + str(i+1) + ": " + card_text)

		# Find matching response in player's hand
		var found_in_hand = false
		for card in player.hand:
			var card_text = ""
			if card.has("response"):
				card_text = card.response
			elif card.has("text"):
				card_text = card.text

			if card_text == response_text:
				print("Found matching response card in player's hand")
				# Remove card from player's hand
				player.remove_card_from_hand(card)
				found_in_hand = true
				break

		if not found_in_hand:
			print("WARNING: Response not found in player's hand")

		# Print hand after removal for debugging
		print("Hand after removal:")
		for i in range(player.hand.size()):
			var card_in_hand_after = player.hand[i]
			var card_text = ""
			if card_in_hand_after.has("response"):
				card_text = card_in_hand_after.response
			elif card_in_hand_after.has("text"):
				card_text = card_in_hand_after.text
			print("  Card " + str(i+1) + ": " + card_text)
	else:
		print("ERROR: Player not found with name: " + player_name)

# Show the room code when a game is created
func show_room_code(game_id = null, room_code_param = null):
	print("Game created, showing room code. Game ID: ", game_id)

	# Get the room code from the parameter or the multiplayer manager
	if room_code_param != null:
		room_code = room_code_param
		print("Room code from parameter: ", room_code)
	elif multiplayer_manager:
		room_code = multiplayer_manager.get_room_code()
		print("Room code from multiplayer manager: ", room_code)

	# Update the room code display if it exists
	if room_code_display:
		room_code_display.set_room_code(room_code)
		room_code_display.visible = true

# Force the current prompt display to be visible - call this whenever we need to ensure the prompt is showing
func ensure_prompt_visible():
	print("\n==== ENSURING PROMPT VISIBILITY ====")

	# Check if we have a current prompt
	if not current_prompt or not current_prompt.has("prompt"):
		print("No current prompt to display")
		return

	# Debug current state
	print("Current prompt: ", current_prompt.get("prompt"))

	# First check if the current_prompt_display node exists
	if current_prompt_display == null:
		print("CurrentPromptDisplay is null, trying to find it")
		current_prompt_display = get_node_or_null("CurrentPromptDisplay")

		# Fallback: search all children
		if current_prompt_display == null:
			print("Still null after get_node_or_null, searching all children")
			for child in get_children():
				print("Checking child: ", child.name)
				if child.name == "CurrentPromptDisplay":
					current_prompt_display = child
					print("Found CurrentPromptDisplay in children")
					break

	# If we now have a valid reference
	if current_prompt_display:
		print("CurrentPromptDisplay node found, setting text: ", current_prompt.get("prompt"))

		# Set the prompt text directly
		current_prompt_display.text = current_prompt.get("prompt", "No prompt available")

		# Force visibility through multiple methods
		current_prompt_display.visible = true
		current_prompt_display.show()
		current_prompt_display.modulate.a = 1.0  # Ensure not transparent

		# Make sure it's rendering on top
		current_prompt_display.z_index = 100

		# Set a more noticeable appearance for debugging
		current_prompt_display.add_theme_color_override("font_color", Color(1, 1, 1))
		current_prompt_display.add_theme_font_size_override("font_size", 48)  # Larger font

		# We no longer update the judge asks label here since we do it in select_and_display_prompt
		# This prevents overriding the judge name with potentially outdated information
		# update_judge_asks_label()

		print("CurrentPromptDisplay properties:")
		print("- text: ", current_prompt_display.text)
		print("- visible: ", current_prompt_display.visible)
		print("- modulate: ", current_prompt_display.modulate)
		print("- global position: ", current_prompt_display.global_position)
		print("- size: ", current_prompt_display.size)
	else:
		print("ERROR: CurrentPromptDisplay node STILL not found!")

		# As a fallback, create a new label if it doesn't exist
		if not has_node("CurrentPromptDisplay"):
			print("Creating a new CurrentPromptDisplay label")
			var new_label = Label.new()
			new_label.name = "CurrentPromptDisplay"
			new_label.text = current_prompt.get("prompt", "No prompt available")
			new_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
			new_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
			new_label.add_theme_font_size_override("font_size", 28)
			new_label.set_anchors_and_offsets_preset(Control.PRESET_TOP_WIDE)
			new_label.offset_top = 100
			new_label.offset_bottom = 150
			new_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
			add_child(new_label)
			current_prompt_display = new_label
			print("Created new CurrentPromptDisplay label")

	# Hide the bottom prompt label to avoid duplication
	if prompt_label:
		prompt_label.visible = false
		print("Hidden bottom prompt label to prevent duplication")

	print("==== PROMPT VISIBILITY CHECK COMPLETE ====\n")

# Update the judge asks label with the current judge's name
func update_judge_asks_label():
	print("\n===== UPDATING JUDGE ASKS LABEL =====")
	
	# Always get the latest judge index from Firebase first
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		# Debug: Get the entire game data to see the structure
		if multiplayer_manager.firebase.has_method("debug_get_entire_game_data"):
			print("DEBUG: Getting entire game data structure...")
			await multiplayer_manager.firebase.debug_get_entire_game_data()

		if multiplayer_manager.firebase.has_method("get_current_judge_index"):
			print("Getting current judge index from Firebase...")
			var firebase_judge_index_raw = await multiplayer_manager.firebase.get_current_judge_index()
			print("Raw Firebase judge index: ", firebase_judge_index_raw)
			
			# Convert to int if needed
			var firebase_judge_index = firebase_judge_index_raw
			if firebase_judge_index_raw is float:
				firebase_judge_index = int(firebase_judge_index_raw)
				print("Converted float to int: ", firebase_judge_index)
			
			# CRITICAL: Update current_judge_index with the Firebase value
			if firebase_judge_index != null and firebase_judge_index >= 0:
				print("Setting current_judge_index to Firebase value: ", firebase_judge_index)
				current_judge_index = firebase_judge_index
	
	# Print all players with their indices for debugging
	print("DEBUG: All players in array:")
	for i in range(players.size()):
		print("  Player ", i, ": ", players[i].pname)
	print("DEBUG: Current judge index is: ", current_judge_index)
	
	# Get the JudgeAsksLabel node if we don't have it
	if not judge_asks_label:
		judge_asks_label = get_node_or_null("JudgeAsksLabel")
		
	# Now set the label with the correct judge's name
	if judge_asks_label and players.size() > 0:
		# Important: Make sure index is valid
		if current_judge_index >= 0 and current_judge_index < players.size():
			var judge_name = players[current_judge_index].pname
			print("Setting JudgeAsksLabel with name: ", judge_name)
			judge_asks_label.text = judge_name + " asks:"
			judge_asks_label.visible = true
		else:
			print("ERROR: Invalid judge index: ", current_judge_index, " for players array of size ", players.size())
			# Fallback to player at index 1 if available
			if players.size() > 1:
				var judge_name = players[1].pname
				print("FALLBACK: Using player at index 1 as judge: ", judge_name)
				judge_asks_label.text = judge_name + " asks:"
				judge_asks_label.visible = true
			# Last resort: use player 0
			elif players.size() > 0:
				var judge_name = players[0].pname
				print("LAST RESORT: Using player at index 0 as judge: ", judge_name)
				judge_asks_label.text = judge_name + " asks:"
				judge_asks_label.visible = true
	else:
		print("ERROR: judge_asks_label is null or players array is empty")
	
	print("===== JUDGE ASKS LABEL UPDATE COMPLETE =====\n")
	
func start_new_round():
	print("\n===== STARTING NEW ROUND =====")
	
	# IMPORTANT: Update the judge label first
	await update_judge_asks_label()
	print("Judge asks label updated at start of round")
	
	# Reset round-specific variables
	played_cards.clear()
	should_display_responses = false
	prompt_sent_for_round = current_round

	# Select and display a new prompt
	select_and_display_prompt()

	# Update Firebase with the new round status
	if multiplayer_manager:
		multiplayer_manager.update_game_status("Waiting for Player Responses")

	# Start the timer for player responses
	judge_timer.start()
	
	print("===== NEW ROUND STARTED =====\n")
