func update_judge_asks_label():
	print("\n===== UPDATING JUDGE ASKS LABEL =====")
	
	# Always get the latest judge index from Firebase first
	if is_multiplayer_game and multiplayer_manager and multiplayer_manager.firebase:
		if multiplayer_manager.firebase.has_method("get_current_judge_index"):
			print("Getting current judge index from Firebase...")
			var firebase_judge_index_raw = await multiplayer_manager.firebase.get_current_judge_index()
			print("Raw Firebase judge index: ", firebase_judge_index_raw)
			
			# Convert to int if needed
			var firebase_judge_index = firebase_judge_index_raw
			if firebase_judge_index_raw is float:
				firebase_judge_index = int(firebase_judge_index_raw)
				print("Converted float to int: ", firebase_judge_index)
			
			# CRITICAL: Update current_judge_index with the Firebase value
			if firebase_judge_index != null and firebase_judge_index >= 0:
				print("Setting current_judge_index to Firebase value: ", firebase_judge_index)
				current_judge_index = firebase_judge_index
	
	# Print all players with their indices for debugging
	print("DEBUG: All players in array:")
	for i in range(players.size()):
		print("  Player ", i, ": ", players[i].pname)
	print("DEBUG: Current judge index is: ", current_judge_index)
	
	# Get the JudgeAsksLabel node if we don't have it
	if not judge_asks_label:
		judge_asks_label = get_node_or_null("JudgeAsksLabel")
		
	# Now set the label with the correct judge's name
	if judge_asks_label and players.size() > 0:
		# Important: Make sure index is valid
		if current_judge_index >= 0 and current_judge_index < players.size():
			var judge_name = players[current_judge_index].pname
			print("Setting JudgeAsksLabel with name: ", judge_name)
			judge_asks_label.text = judge_name + " asks:"
			judge_asks_label.visible = true
		else:
			print("ERROR: Invalid judge index: ", current_judge_index, " for players array of size ", players.size())
			# Fallback to player at index 1 if available
			if players.size() > 1:
				var judge_name = players[1].pname
				print("FALLBACK: Using player at index 1 as judge: ", judge_name)
				judge_asks_label.text = judge_name + " asks:"
				judge_asks_label.visible = true
			# Last resort: use player 0
			elif players.size() > 0:
				var judge_name = players[0].pname
				print("LAST RESORT: Using player at index 0 as judge: ", judge_name)
				judge_asks_label.text = judge_name + " asks:"
				judge_asks_label.visible = true
	else:
		print("ERROR: judge_asks_label is null or players array is empty")
	
	print("===== JUDGE ASKS LABEL UPDATE COMPLETE =====\n")
